// 通用响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 分页参数
export interface PageParams {
  page: number
  pageSize: number
}

// 分页响应
export interface PageResponse<T> {
  list: T[]
  total: number
  page: number
  pageSize: number
}

// 用户相关类型
export interface User {
  id: number
  username: string
  nickname: string
  avatar: string
  email: string
  phone: string
  gender: 0 | 1 | 2 // 0: 未知, 1: 男, 2: 女
  birthday: string
  signature: string
  level: number
  exp: number
  coin: number
  vipLevel: number
  vipExpireTime: string
  status: 0 | 1 // 0: 禁用, 1: 正常
  createTime: string
  updateTime: string
}

// 登录表单
export interface LoginForm {
  username: string
  password: string
  remember?: boolean
}

// 注册表单
export interface RegisterForm {
  username: string
  password: string
  confirmPassword: string
  email: string
  phone: string
  code: string
}

// 内容类型枚举
export enum ContentType {
  COMIC = 'comic', // 漫画
  NOVEL = 'novel', // 小说
}

// 内容状态枚举
export enum ContentStatus {
  ONGOING = 'ongoing', // 连载中
  COMPLETED = 'completed', // 已完结
  PAUSED = 'paused', // 暂停更新
}

// 内容基础信息
export interface Content {
  id: number
  title: string
  cover: string
  author: string
  description: string
  type: ContentType
  status: ContentStatus
  tags: string[]
  rating: number
  viewCount: number
  favoriteCount: number
  commentCount: number
  chapterCount: number
  lastChapterTitle: string
  lastUpdateTime: string
  createTime: string
  updateTime: string
}

// 章节信息
export interface Chapter {
  id: number
  contentId: number
  title: string
  chapterNumber: number
  content?: string // 小说内容
  images?: string[] // 漫画图片
  wordCount?: number // 字数（小说）
  pageCount?: number // 页数（漫画）
  isVip: boolean
  price: number
  publishTime: string
  createTime: string
  updateTime: string
}

// 评论信息
export interface Comment {
  id: number
  contentId: number
  chapterId?: number
  userId: number
  userNickname: string
  userAvatar: string
  content: string
  likeCount: number
  replyCount: number
  parentId?: number
  replies?: Comment[]
  isLiked: boolean
  createTime: string
}

// 收藏信息
export interface Favorite {
  id: number
  userId: number
  contentId: number
  content: Content
  createTime: string
}

// 阅读历史
export interface ReadHistory {
  id: number
  userId: number
  contentId: number
  chapterId: number
  content: Content
  chapter: Chapter
  progress: number // 阅读进度 (0-100)
  readTime: number // 阅读时长（秒）
  lastReadTime: string
}

// 搜索参数
export interface SearchParams extends PageParams {
  keyword: string
  type?: ContentType
  status?: ContentStatus
  tags?: string[]
  sortBy?: 'update' | 'create' | 'rating' | 'view'
  sortOrder?: 'asc' | 'desc'
}

// 阅读设置
export interface ReadingSettings {
  // 通用设置
  theme: 'light' | 'dark' | 'sepia'
  fontSize: number
  lineHeight: number

  // 小说设置
  fontFamily: string
  backgroundColor: string
  textColor: string
  pageMode: 'scroll' | 'page'

  // 漫画设置
  readMode: 'vertical' | 'horizontal'
  autoNext: boolean
  brightness: number
}

// Banner 轮播图
export interface Banner {
  id: number
  title: string
  image: string
  link: string
  type: 'content' | 'url'
  contentId?: number
  sort: number
  status: 0 | 1
  createTime: string
}

// 分类信息
export interface Category {
  id: number
  name: string
  slug: string
  description: string
  icon: string
  sort: number
  parentId?: number
  children?: Category[]
  contentCount: number
  status: 0 | 1
  createTime: string
}

// 路由元信息
declare module 'vue-router' {
  interface RouteMeta {
    title?: string
    requiresAuth?: boolean
    hideForAuth?: boolean
    hideHeader?: boolean
    hideTabbar?: boolean
  }
}
