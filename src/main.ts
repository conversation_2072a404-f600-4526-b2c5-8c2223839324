import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

// Vant UI 组件库
import {
  Button,
  NavBar,
  Tabbar,
  TabbarItem,
  Search,
  Swipe,
  SwipeItem,
  Grid,
  GridItem,
  Image as VanImage,
  Card,
  Tag,
  List,
  Cell,
  CellGroup,
  Icon,
  Loading,
  Empty,
  Popup,
  Toast,
  Dialog,
  Form,
  Field,
  Checkbox,
  Radio,
  RadioGroup,
  Picker,
  DatePicker,
  Uploader,
  Rate,
  Slider,
  Switch,
  Stepper,
  ActionSheet,
  ShareSheet,
  NoticeBar,
  Progress,
  Circle,
  Divider,
  PullRefresh,
  BackTop,
  Sticky,
  IndexBar,
  IndexAnchor,
  Sidebar,
  SidebarItem,
  TreeSelect,
  ContactCard,
  ContactList,
  ContactEdit,
  Calendar,
  Cascader,
  ConfigProvider,
  Locale,
} from 'vant'

import App from './App.vue'
import router from './router'

const app = createApp(App)

// 注册 Vant 组件
app.use(Button)
app.use(NavBar)
app.use(Tabbar)
app.use(TabbarItem)
app.use(Search)
app.use(Swipe)
app.use(SwipeItem)
app.use(Grid)
app.use(GridItem)
app.use(VanImage)
app.use(Card)
app.use(Tag)
app.use(List)
app.use(Cell)
app.use(CellGroup)
app.use(Icon)
app.use(Loading)
app.use(Empty)
app.use(Popup)
app.use(Toast)
app.use(Dialog)
app.use(Form)
app.use(Field)
app.use(Checkbox)
app.use(Radio)
app.use(RadioGroup)
app.use(Picker)
app.use(DatePicker)
app.use(Uploader)
app.use(Rate)
app.use(Slider)
app.use(Switch)
app.use(Stepper)
app.use(ActionSheet)
app.use(ShareSheet)
app.use(NoticeBar)
app.use(Progress)
app.use(Circle)
app.use(Divider)
app.use(PullRefresh)
app.use(BackTop)
app.use(Sticky)
app.use(IndexBar)
app.use(IndexAnchor)
app.use(Sidebar)
app.use(SidebarItem)
app.use(TreeSelect)
app.use(ContactCard)
app.use(ContactList)
app.use(ContactEdit)
app.use(Calendar)
app.use(Cascader)
app.use(ConfigProvider)

app.use(createPinia())
app.use(router)

app.mount('#app')
