<template>
  <div class="not-found-page">
    <div class="container mx-auto px-4 py-16 text-center">
      <div class="max-w-md mx-auto">
        <div class="mb-8">
          <div class="text-8xl font-bold text-primary-500 mb-4">404</div>
          <h1 class="text-2xl font-bold text-gray-800 mb-2">页面未找到</h1>
          <p class="text-gray-600 mb-8">
            抱歉，您访问的页面不存在或已被删除。
          </p>
        </div>
        
        <div class="space-y-4">
          <router-link
            to="/"
            class="inline-block px-6 py-3 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors"
          >
            返回首页
          </router-link>
          
          <div class="text-sm text-gray-500">
            或者您可以尝试：
          </div>
          
          <div class="flex flex-col sm:flex-row gap-2 justify-center">
            <router-link
              to="/category/comic"
              class="px-4 py-2 text-primary-600 hover:text-primary-700 transition-colors"
            >
              浏览漫画
            </router-link>
            <router-link
              to="/category/novel"
              class="px-4 py-2 text-primary-600 hover:text-primary-700 transition-colors"
            >
              浏览小说
            </router-link>
            <router-link
              to="/ranking"
              class="px-4 py-2 text-primary-600 hover:text-primary-700 transition-colors"
            >
              查看排行榜
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 404 页面组件
</script>

<style scoped>
.not-found-page {
  @apply min-h-screen bg-gray-50 flex items-center justify-center;
}
</style>
