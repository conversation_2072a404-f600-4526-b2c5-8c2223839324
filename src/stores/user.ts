import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import type { User, LoginForm, RegisterForm } from '@/types'
import { request } from '@/utils/request'

export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref<User | null>(null)
  const token = ref<string>('')
  const isLoggedIn = computed(() => !!token.value && !!userInfo.value)

  // 初始化用户信息
  const initUserInfo = () => {
    const savedToken = localStorage.getItem('token')
    const savedUserInfo = localStorage.getItem('userInfo')
    
    if (savedToken && savedUserInfo) {
      token.value = savedToken
      try {
        userInfo.value = JSON.parse(savedUserInfo)
      } catch (error) {
        console.error('解析用户信息失败:', error)
        clearUserInfo()
      }
    }
  }

  // 清除用户信息
  const clearUserInfo = () => {
    userInfo.value = null
    token.value = ''
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
  }

  // 登录
  const login = async (loginForm: LoginForm) => {
    try {
      const response = await request.post<{
        token: string
        userInfo: User
      }>('/auth/login', loginForm)
      
      token.value = response.token
      userInfo.value = response.userInfo
      
      // 保存到本地存储
      localStorage.setItem('token', response.token)
      localStorage.setItem('userInfo', JSON.stringify(response.userInfo))
      
      return response
    } catch (error) {
      throw error
    }
  }

  // 注册
  const register = async (registerForm: RegisterForm) => {
    try {
      const response = await request.post<{
        token: string
        userInfo: User
      }>('/auth/register', registerForm)
      
      token.value = response.token
      userInfo.value = response.userInfo
      
      // 保存到本地存储
      localStorage.setItem('token', response.token)
      localStorage.setItem('userInfo', JSON.stringify(response.userInfo))
      
      return response
    } catch (error) {
      throw error
    }
  }

  // 退出登录
  const logout = async () => {
    try {
      await request.post('/auth/logout')
    } catch (error) {
      console.error('退出登录失败:', error)
    } finally {
      clearUserInfo()
    }
  }

  // 更新用户信息
  const updateUserInfo = async (updateData: Partial<User>) => {
    try {
      const response = await request.put<User>('/user/profile', updateData)
      userInfo.value = response
      localStorage.setItem('userInfo', JSON.stringify(response))
      return response
    } catch (error) {
      throw error
    }
  }

  // 获取用户信息
  const getUserInfo = async () => {
    try {
      const response = await request.get<User>('/user/profile')
      userInfo.value = response
      localStorage.setItem('userInfo', JSON.stringify(response))
      return response
    } catch (error) {
      throw error
    }
  }

  return {
    userInfo,
    token,
    isLoggedIn,
    initUserInfo,
    clearUserInfo,
    login,
    register,
    logout,
    updateUserInfo,
    getUserInfo
  }
})
