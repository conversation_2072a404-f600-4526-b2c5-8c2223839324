import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import type { 
  Content, 
  Chapter, 
  Comment, 
  Favorite, 
  ReadHistory, 
  SearchParams, 
  PageResponse,
  ContentType,
  ContentStatus
} from '@/types'
import { request } from '@/utils/request'

export const useContentStore = defineStore('content', () => {
  // 状态
  const contentList = ref<Content[]>([])
  const currentContent = ref<Content | null>(null)
  const chapters = ref<Chapter[]>([])
  const currentChapter = ref<Chapter | null>(null)
  const comments = ref<Comment[]>([])
  const favorites = ref<Favorite[]>([])
  const readHistory = ref<ReadHistory[]>([])
  const searchResults = ref<Content[]>([])
  const loading = ref(false)
  const total = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(20)

  // 计算属性
  const hasMore = computed(() => contentList.value.length < total.value)
  const isContentFavorited = computed(() => {
    if (!currentContent.value) return false
    return favorites.value.some(fav => fav.contentId === currentContent.value!.id)
  })

  // 获取内容列表
  const getContentList = async (params: {
    type?: ContentType
    status?: ContentStatus
    tags?: string[]
    page?: number
    pageSize?: number
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
  } = {}) => {
    try {
      loading.value = true
      const response = await request.get<PageResponse<Content>>('/content/list', {
        page: params.page || 1,
        pageSize: params.pageSize || pageSize.value,
        ...params
      })
      
      if (params.page === 1) {
        contentList.value = response.list
      } else {
        contentList.value.push(...response.list)
      }
      
      total.value = response.total
      currentPage.value = response.page
      
      return response
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取内容详情
  const getContentDetail = async (id: number) => {
    try {
      loading.value = true
      const response = await request.get<Content>(`/content/${id}`)
      currentContent.value = response
      return response
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取章节列表
  const getChapterList = async (contentId: number) => {
    try {
      const response = await request.get<Chapter[]>(`/content/${contentId}/chapters`)
      chapters.value = response
      return response
    } catch (error) {
      throw error
    }
  }

  // 获取章节详情
  const getChapterDetail = async (contentId: number, chapterId: number) => {
    try {
      loading.value = true
      const response = await request.get<Chapter>(`/content/${contentId}/chapter/${chapterId}`)
      currentChapter.value = response
      return response
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 搜索内容
  const searchContent = async (params: SearchParams) => {
    try {
      loading.value = true
      const response = await request.get<PageResponse<Content>>('/content/search', params)
      
      if (params.page === 1) {
        searchResults.value = response.list
      } else {
        searchResults.value.push(...response.list)
      }
      
      total.value = response.total
      currentPage.value = response.page
      
      return response
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取评论列表
  const getComments = async (contentId: number, chapterId?: number) => {
    try {
      const url = chapterId 
        ? `/content/${contentId}/chapter/${chapterId}/comments`
        : `/content/${contentId}/comments`
      const response = await request.get<Comment[]>(url)
      comments.value = response
      return response
    } catch (error) {
      throw error
    }
  }

  // 添加评论
  const addComment = async (contentId: number, content: string, chapterId?: number, parentId?: number) => {
    try {
      const response = await request.post<Comment>('/comment/add', {
        contentId,
        chapterId,
        content,
        parentId
      })
      
      // 更新评论列表
      if (parentId) {
        // 回复评论
        const parentComment = comments.value.find(c => c.id === parentId)
        if (parentComment) {
          if (!parentComment.replies) parentComment.replies = []
          parentComment.replies.push(response)
          parentComment.replyCount++
        }
      } else {
        // 新评论
        comments.value.unshift(response)
      }
      
      return response
    } catch (error) {
      throw error
    }
  }

  // 点赞评论
  const likeComment = async (commentId: number) => {
    try {
      await request.post(`/comment/${commentId}/like`)
      const comment = comments.value.find(c => c.id === commentId)
      if (comment) {
        comment.isLiked = !comment.isLiked
        comment.likeCount += comment.isLiked ? 1 : -1
      }
    } catch (error) {
      throw error
    }
  }

  // 收藏/取消收藏
  const toggleFavorite = async (contentId: number) => {
    try {
      const isFavorited = favorites.value.some(fav => fav.contentId === contentId)
      
      if (isFavorited) {
        await request.delete(`/favorite/${contentId}`)
        favorites.value = favorites.value.filter(fav => fav.contentId !== contentId)
        if (currentContent.value && currentContent.value.id === contentId) {
          currentContent.value.favoriteCount--
        }
      } else {
        const response = await request.post<Favorite>('/favorite/add', { contentId })
        favorites.value.unshift(response)
        if (currentContent.value && currentContent.value.id === contentId) {
          currentContent.value.favoriteCount++
        }
      }
      
      return !isFavorited
    } catch (error) {
      throw error
    }
  }

  // 获取收藏列表
  const getFavorites = async () => {
    try {
      const response = await request.get<Favorite[]>('/favorite/list')
      favorites.value = response
      return response
    } catch (error) {
      throw error
    }
  }

  // 添加阅读历史
  const addReadHistory = async (contentId: number, chapterId: number, progress: number = 0) => {
    try {
      const response = await request.post<ReadHistory>('/history/add', {
        contentId,
        chapterId,
        progress
      })
      
      // 更新本地历史记录
      const existingIndex = readHistory.value.findIndex(h => h.contentId === contentId)
      if (existingIndex >= 0) {
        readHistory.value[existingIndex] = response
      } else {
        readHistory.value.unshift(response)
      }
      
      return response
    } catch (error) {
      throw error
    }
  }

  // 获取阅读历史
  const getReadHistory = async () => {
    try {
      const response = await request.get<ReadHistory[]>('/history/list')
      readHistory.value = response
      return response
    } catch (error) {
      throw error
    }
  }

  // 清空状态
  const clearState = () => {
    contentList.value = []
    currentContent.value = null
    chapters.value = []
    currentChapter.value = null
    comments.value = []
    searchResults.value = []
    total.value = 0
    currentPage.value = 1
  }

  return {
    // 状态
    contentList,
    currentContent,
    chapters,
    currentChapter,
    comments,
    favorites,
    readHistory,
    searchResults,
    loading,
    total,
    currentPage,
    pageSize,
    
    // 计算属性
    hasMore,
    isContentFavorited,
    
    // 方法
    getContentList,
    getContentDetail,
    getChapterList,
    getChapterDetail,
    searchContent,
    getComments,
    addComment,
    likeComment,
    toggleFavorite,
    getFavorites,
    addReadHistory,
    getReadHistory,
    clearState
  }
})
