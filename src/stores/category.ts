import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import type { Category, Banner, ContentType } from '@/types'
import { request } from '@/utils/request'

export const useCategoryStore = defineStore('category', () => {
  // 状态
  const categories = ref<Category[]>([])
  const banners = ref<Banner[]>([])
  const loading = ref(false)

  // 计算属性
  const comicCategories = computed(() => 
    categories.value.filter(cat => cat.name.includes('漫画') || cat.slug.includes('comic'))
  )
  
  const novelCategories = computed(() => 
    categories.value.filter(cat => cat.name.includes('小说') || cat.slug.includes('novel'))
  )

  const topLevelCategories = computed(() => 
    categories.value.filter(cat => !cat.parentId)
  )

  const activeBanners = computed(() => 
    banners.value.filter(banner => banner.status === 1).sort((a, b) => a.sort - b.sort)
  )

  // 获取分类列表
  const getCategories = async () => {
    try {
      loading.value = true
      const response = await request.get<Category[]>('/category/list')
      categories.value = response
      return response
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取轮播图
  const getBanners = async () => {
    try {
      const response = await request.get<Banner[]>('/banner/list')
      banners.value = response
      return response
    } catch (error) {
      throw error
    }
  }

  // 根据类型获取分类
  const getCategoriesByType = (type: ContentType) => {
    return categories.value.filter(cat => {
      if (type === 'comic') {
        return cat.name.includes('漫画') || cat.slug.includes('comic')
      } else if (type === 'novel') {
        return cat.name.includes('小说') || cat.slug.includes('novel')
      }
      return true
    })
  }

  // 根据ID获取分类
  const getCategoryById = (id: number) => {
    return categories.value.find(cat => cat.id === id)
  }

  // 根据slug获取分类
  const getCategoryBySlug = (slug: string) => {
    return categories.value.find(cat => cat.slug === slug)
  }

  // 获取子分类
  const getChildCategories = (parentId: number) => {
    return categories.value.filter(cat => cat.parentId === parentId)
  }

  // 构建分类树
  const buildCategoryTree = (parentId?: number): Category[] => {
    const children = categories.value.filter(cat => cat.parentId === parentId)
    return children.map(cat => ({
      ...cat,
      children: buildCategoryTree(cat.id)
    }))
  }

  // 获取分类路径
  const getCategoryPath = (categoryId: number): Category[] => {
    const path: Category[] = []
    let current = getCategoryById(categoryId)
    
    while (current) {
      path.unshift(current)
      current = current.parentId ? getCategoryById(current.parentId) : null
    }
    
    return path
  }

  // 搜索分类
  const searchCategories = (keyword: string) => {
    const lowerKeyword = keyword.toLowerCase()
    return categories.value.filter(cat => 
      cat.name.toLowerCase().includes(lowerKeyword) ||
      cat.description.toLowerCase().includes(lowerKeyword) ||
      cat.slug.toLowerCase().includes(lowerKeyword)
    )
  }

  // 获取热门分类（根据内容数量排序）
  const getPopularCategories = (limit: number = 10) => {
    return [...categories.value]
      .sort((a, b) => b.contentCount - a.contentCount)
      .slice(0, limit)
  }

  // 初始化分类数据
  const initCategories = async () => {
    try {
      await Promise.all([
        getCategories(),
        getBanners()
      ])
    } catch (error) {
      console.error('初始化分类数据失败:', error)
    }
  }

  return {
    // 状态
    categories,
    banners,
    loading,
    
    // 计算属性
    comicCategories,
    novelCategories,
    topLevelCategories,
    activeBanners,
    
    // 方法
    getCategories,
    getBanners,
    getCategoriesByType,
    getCategoryById,
    getCategoryBySlug,
    getChildCategories,
    buildCategoryTree,
    getCategoryPath,
    searchCategories,
    getPopularCategories,
    initCategories
  }
})
