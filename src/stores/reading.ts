import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import type { ReadingSettings } from '@/types'

export const useReadingStore = defineStore('reading', () => {
  // 默认阅读设置
  const defaultSettings: ReadingSettings = {
    // 通用设置
    theme: 'light',
    fontSize: 16,
    lineHeight: 1.6,
    
    // 小说设置
    fontFamily: 'system-ui',
    backgroundColor: '#ffffff',
    textColor: '#333333',
    pageMode: 'scroll',
    
    // 漫画设置
    readMode: 'vertical',
    autoNext: true,
    brightness: 100,
  }

  // 状态
  const settings = ref<ReadingSettings>({ ...defaultSettings })
  const isReading = ref(false)
  const currentContentId = ref<number | null>(null)
  const currentChapterId = ref<number | null>(null)
  const readingProgress = ref(0)
  const showSettings = ref(false)

  // 计算属性
  const themeClass = computed(() => {
    switch (settings.value.theme) {
      case 'dark':
        return 'reading-theme-dark'
      case 'sepia':
        return 'reading-theme-sepia'
      default:
        return 'reading-theme-light'
    }
  })

  const fontSizeClass = computed(() => {
    const size = settings.value.fontSize
    if (size <= 12) return 'text-xs'
    if (size <= 14) return 'text-sm'
    if (size <= 16) return 'text-base'
    if (size <= 18) return 'text-lg'
    if (size <= 20) return 'text-xl'
    return 'text-2xl'
  })

  const lineHeightStyle = computed(() => ({
    lineHeight: settings.value.lineHeight
  }))

  const readingStyle = computed(() => ({
    fontSize: `${settings.value.fontSize}px`,
    lineHeight: settings.value.lineHeight,
    fontFamily: settings.value.fontFamily,
    backgroundColor: settings.value.backgroundColor,
    color: settings.value.textColor,
    filter: `brightness(${settings.value.brightness}%)`
  }))

  // 初始化设置
  const initSettings = () => {
    const savedSettings = localStorage.getItem('readingSettings')
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings)
        settings.value = { ...defaultSettings, ...parsed }
      } catch (error) {
        console.error('解析阅读设置失败:', error)
        settings.value = { ...defaultSettings }
      }
    }
  }

  // 保存设置
  const saveSettings = () => {
    localStorage.setItem('readingSettings', JSON.stringify(settings.value))
  }

  // 更新设置
  const updateSettings = (newSettings: Partial<ReadingSettings>) => {
    settings.value = { ...settings.value, ...newSettings }
    saveSettings()
  }

  // 重置设置
  const resetSettings = () => {
    settings.value = { ...defaultSettings }
    saveSettings()
  }

  // 设置主题
  const setTheme = (theme: 'light' | 'dark' | 'sepia') => {
    const themeSettings: Partial<ReadingSettings> = {
      theme,
    }

    switch (theme) {
      case 'dark':
        themeSettings.backgroundColor = '#1a1a1a'
        themeSettings.textColor = '#e5e5e5'
        break
      case 'sepia':
        themeSettings.backgroundColor = '#f7f3e9'
        themeSettings.textColor = '#5c4b37'
        break
      default:
        themeSettings.backgroundColor = '#ffffff'
        themeSettings.textColor = '#333333'
    }

    updateSettings(themeSettings)
  }

  // 调整字体大小
  const adjustFontSize = (delta: number) => {
    const newSize = Math.max(12, Math.min(24, settings.value.fontSize + delta))
    updateSettings({ fontSize: newSize })
  }

  // 调整行高
  const adjustLineHeight = (delta: number) => {
    const newLineHeight = Math.max(1.2, Math.min(2.0, settings.value.lineHeight + delta))
    updateSettings({ lineHeight: newLineHeight })
  }

  // 调整亮度
  const adjustBrightness = (delta: number) => {
    const newBrightness = Math.max(50, Math.min(150, settings.value.brightness + delta))
    updateSettings({ brightness: newBrightness })
  }

  // 切换阅读模式
  const toggleReadMode = () => {
    const newMode = settings.value.readMode === 'vertical' ? 'horizontal' : 'vertical'
    updateSettings({ readMode: newMode })
  }

  // 切换翻页模式
  const togglePageMode = () => {
    const newMode = settings.value.pageMode === 'scroll' ? 'page' : 'scroll'
    updateSettings({ pageMode: newMode })
  }

  // 切换自动下一章
  const toggleAutoNext = () => {
    updateSettings({ autoNext: !settings.value.autoNext })
  }

  // 开始阅读
  const startReading = (contentId: number, chapterId: number) => {
    isReading.value = true
    currentContentId.value = contentId
    currentChapterId.value = chapterId
    readingProgress.value = 0
  }

  // 结束阅读
  const stopReading = () => {
    isReading.value = false
    currentContentId.value = null
    currentChapterId.value = null
    readingProgress.value = 0
  }

  // 更新阅读进度
  const updateProgress = (progress: number) => {
    readingProgress.value = Math.max(0, Math.min(100, progress))
  }

  // 显示/隐藏设置面板
  const toggleSettings = () => {
    showSettings.value = !showSettings.value
  }

  // 预设主题
  const presetThemes = [
    {
      name: '默认',
      theme: 'light' as const,
      backgroundColor: '#ffffff',
      textColor: '#333333'
    },
    {
      name: '夜间',
      theme: 'dark' as const,
      backgroundColor: '#1a1a1a',
      textColor: '#e5e5e5'
    },
    {
      name: '护眼',
      theme: 'sepia' as const,
      backgroundColor: '#f7f3e9',
      textColor: '#5c4b37'
    },
    {
      name: '青空',
      theme: 'light' as const,
      backgroundColor: '#e8f4fd',
      textColor: '#2c3e50'
    },
    {
      name: '粉红',
      theme: 'light' as const,
      backgroundColor: '#fdf2f8',
      textColor: '#831843'
    }
  ]

  // 应用预设主题
  const applyPresetTheme = (preset: typeof presetThemes[0]) => {
    updateSettings({
      theme: preset.theme,
      backgroundColor: preset.backgroundColor,
      textColor: preset.textColor
    })
  }

  return {
    // 状态
    settings,
    isReading,
    currentContentId,
    currentChapterId,
    readingProgress,
    showSettings,
    
    // 计算属性
    themeClass,
    fontSizeClass,
    lineHeightStyle,
    readingStyle,
    
    // 预设
    presetThemes,
    
    // 方法
    initSettings,
    saveSettings,
    updateSettings,
    resetSettings,
    setTheme,
    adjustFontSize,
    adjustLineHeight,
    adjustBrightness,
    toggleReadMode,
    togglePageMode,
    toggleAutoNext,
    startReading,
    stopReading,
    updateProgress,
    toggleSettings,
    applyPresetTheme
  }
})
