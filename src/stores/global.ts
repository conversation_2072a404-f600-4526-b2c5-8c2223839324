import { ref } from 'vue'
import { defineStore } from 'pinia'

export const useGlobalStore = defineStore('global', () => {
  // 全局加载状态
  const loading = ref(false)
  
  // 网络状态
  const isOnline = ref(navigator.onLine)
  
  // 主题模式
  const theme = ref<'light' | 'dark'>('light')
  
  // 设备类型
  const isMobile = ref(false)
  
  // 设置加载状态
  const setLoading = (status: boolean) => {
    loading.value = status
  }
  
  // 设置网络状态
  const setOnlineStatus = (status: boolean) => {
    isOnline.value = status
  }
  
  // 切换主题
  const toggleTheme = () => {
    theme.value = theme.value === 'light' ? 'dark' : 'light'
    localStorage.setItem('theme', theme.value)
    updateThemeClass()
  }
  
  // 设置主题
  const setTheme = (newTheme: 'light' | 'dark') => {
    theme.value = newTheme
    localStorage.setItem('theme', newTheme)
    updateThemeClass()
  }
  
  // 更新主题类名
  const updateThemeClass = () => {
    const html = document.documentElement
    if (theme.value === 'dark') {
      html.classList.add('dark')
    } else {
      html.classList.remove('dark')
    }
  }
  
  // 设置设备类型
  const setMobile = (mobile: boolean) => {
    isMobile.value = mobile
  }
  
  // 初始化全局状态
  const initGlobalState = () => {
    // 初始化主题
    const savedTheme = localStorage.getItem('theme') as 'light' | 'dark' | null
    if (savedTheme) {
      setTheme(savedTheme)
    }
    
    // 监听网络状态变化
    window.addEventListener('online', () => setOnlineStatus(true))
    window.addEventListener('offline', () => setOnlineStatus(false))
    
    // 检测设备类型
    const checkMobile = () => {
      setMobile(window.innerWidth < 768)
    }
    checkMobile()
    window.addEventListener('resize', checkMobile)
  }
  
  return {
    loading,
    isOnline,
    theme,
    isMobile,
    setLoading,
    setOnlineStatus,
    toggleTheme,
    setTheme,
    setMobile,
    initGlobalState
  }
})
