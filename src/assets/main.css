@import './base.css';
@import 'tailwindcss/preflight';
@import 'tailwindcss/utilities';

/* 全局样式 */
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  font-family:
    'Inter',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f8fafc;
}

#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式图片 */
img {
  max-width: 100%;
  height: auto;
}

/* 链接样式 */
a {
  color: #0ea5e9;
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: #0284c7;
}

/* 按钮基础样式 */
.btn {
  @apply px-4 py-2 rounded-lg font-medium transition-all duration-200 cursor-pointer;
}

.btn-primary {
  @apply bg-primary-500 text-white hover:bg-primary-600 active:bg-primary-700;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-800 hover:bg-gray-300 active:bg-gray-400;
}

/* 卡片样式 */
.card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-4;
}

.card-hover {
  @apply hover:shadow-md transition-shadow duration-200;
}

/* 文本截断 */
.text-ellipsis {
  @apply truncate;
}

.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-ellipsis-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
