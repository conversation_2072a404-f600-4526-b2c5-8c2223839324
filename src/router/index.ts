import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'Home',
      component: () => import('@/views/HomePage.vue'),
      meta: {
        title: '首页 - 墨文阁',
        requiresAuth: false,
      },
    },
    {
      path: '/category/:type?',
      name: 'Category',
      component: () => import('@/views/CategoryPage.vue'),
      meta: {
        title: '分类 - 墨文阁',
        requiresAuth: false,
      },
    },
    {
      path: '/content/:id',
      name: 'ContentDetail',
      component: () => import('@/views/ContentDetailPage.vue'),
      meta: {
        title: '详情 - 墨文阁',
        requiresAuth: false,
      },
    },
    {
      path: '/read/:id/:chapterId',
      name: 'Reading',
      component: () => import('@/views/ReadingPage.vue'),
      meta: {
        title: '阅读 - 墨文阁',
        requiresAuth: false,
        hideHeader: true,
        hideTabbar: true,
      },
    },
    {
      path: '/search',
      name: 'Search',
      component: () => import('@/views/SearchPage.vue'),
      meta: {
        title: '搜索 - 墨文阁',
        requiresAuth: false,
      },
    },
    {
      path: '/ranking',
      name: 'Ranking',
      component: () => import('@/views/RankingPage.vue'),
      meta: {
        title: '排行榜 - 墨文阁',
        requiresAuth: false,
      },
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/LoginPage.vue'),
      meta: {
        title: '登录 - 墨文阁',
        requiresAuth: false,
        hideForAuth: true,
      },
    },
    {
      path: '/register',
      name: 'Register',
      component: () => import('@/views/RegisterPage.vue'),
      meta: {
        title: '注册 - 墨文阁',
        requiresAuth: false,
        hideForAuth: true,
      },
    },
    {
      path: '/profile',
      name: 'Profile',
      component: () => import('@/views/ProfilePage.vue'),
      meta: {
        title: '个人中心 - 墨文阁',
        requiresAuth: true,
      },
    },
    {
      path: '/favorites',
      name: 'Favorites',
      component: () => import('@/views/FavoritesPage.vue'),
      meta: {
        title: '我的收藏 - 墨文阁',
        requiresAuth: true,
      },
    },
    {
      path: '/history',
      name: 'History',
      component: () => import('@/views/HistoryPage.vue'),
      meta: {
        title: '阅读历史 - 墨文阁',
        requiresAuth: true,
      },
    },
    {
      path: '/bookshelf',
      name: 'Bookshelf',
      component: () => import('@/views/BookshelfPage.vue'),
      meta: {
        title: '我的书架 - 墨文阁',
        requiresAuth: true,
      },
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/views/NotFoundPage.vue'),
      meta: {
        title: '页面未找到 - 墨文阁',
        requiresAuth: false,
      },
    },
  ],
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()

  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title as string
  }

  // 检查是否需要登录
  if (to.meta.requiresAuth && !userStore.isLoggedIn) {
    next({
      path: '/login',
      query: { redirect: to.fullPath },
    })
    return
  }

  // 已登录用户访问登录/注册页面时重定向到首页
  if (to.meta.hideForAuth && userStore.isLoggedIn) {
    next('/')
    return
  }

  next()
})

export default router
