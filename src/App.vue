<script setup lang="ts">
import { onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { useGlobalStore } from '@/stores/global'
import AppLayout from '@/components/layout/AppLayout.vue'

const userStore = useUserStore()
const globalStore = useGlobalStore()

onMounted(() => {
  // 初始化用户信息
  userStore.initUserInfo()

  // 初始化全局状态
  globalStore.initGlobalState()
})
</script>

<template>
  <AppLayout />
</template>
