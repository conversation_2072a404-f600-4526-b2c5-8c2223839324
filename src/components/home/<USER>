<template>
  <div class="hot-recommend">
    <!-- 标题 -->
    <div class="flex items-center justify-between mb-4">
      <h2 class="text-lg font-bold text-gray-900 flex items-center">
        <van-icon name="fire" class="text-red-500 mr-2" />
        热门推荐
      </h2>
      <router-link
        to="/ranking"
        class="text-sm text-primary-600 hover:text-primary-700 flex items-center"
      >
        更多
        <van-icon name="arrow" class="ml-1" />
      </router-link>
    </div>
    
    <!-- 内容网格 -->
    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
      <ContentCard
        v-for="content in hotContents"
        :key="content.id"
        :content="content"
      />
    </div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
      <div
        v-for="i in 6"
        :key="i"
        class="animate-pulse"
      >
        <div class="bg-gray-200 aspect-[3/4] rounded-lg mb-3"></div>
        <div class="space-y-2">
          <div class="h-4 bg-gray-200 rounded"></div>
          <div class="h-3 bg-gray-200 rounded w-2/3"></div>
          <div class="h-3 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    </div>
    
    <!-- 空状态 -->
    <div v-if="!hotContents.length && !loading" class="text-center py-12">
      <van-empty description="暂无热门内容" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useContentStore } from '@/stores/content'
import ContentCard from '@/components/common/ContentCard.vue'
import type { Content } from '@/types'

const contentStore = useContentStore()

// 状态
const hotContents = ref<Content[]>([])
const loading = ref(false)

// 获取热门内容
const getHotContents = async () => {
  try {
    loading.value = true
    const response = await contentStore.getContentList({
      page: 1,
      pageSize: 12,
      sortBy: 'view',
      sortOrder: 'desc'
    })
    hotContents.value = response.list
  } catch (error) {
    console.error('获取热门内容失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getHotContents()
})
</script>

<style scoped>
.hot-recommend {
  @apply mb-8;
}
</style>
