<template>
  <div class="banner-swiper">
    <van-swipe
      :autoplay="3000"
      :show-indicators="true"
      indicator-color="rgba(255, 255, 255, 0.5)"
      class="h-48 md:h-64"
    >
      <van-swipe-item
        v-for="banner in banners"
        :key="banner.id"
        @click="handleBannerClick(banner)"
      >
        <div class="relative w-full h-full overflow-hidden rounded-lg">
          <van-image
            :src="banner.image"
            :alt="banner.title"
            fit="cover"
            class="w-full h-full"
            loading-icon="photo"
            error-icon="photo-fail"
          />
          <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
          <div class="absolute bottom-4 left-4 right-4 text-white">
            <h3 class="text-lg font-bold mb-1 text-ellipsis">{{ banner.title }}</h3>
          </div>
        </div>
      </van-swipe-item>
    </van-swipe>
    
    <!-- 空状态 -->
    <div v-if="!banners.length && !loading" class="h-48 md:h-64 bg-gray-100 rounded-lg flex items-center justify-center">
      <div class="text-center text-gray-500">
        <van-icon name="photo" size="48" class="mb-2" />
        <p>暂无轮播内容</p>
      </div>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="h-48 md:h-64 bg-gray-100 rounded-lg flex items-center justify-center">
      <van-loading size="24px" color="#0ea5e9">加载中...</van-loading>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useCategoryStore } from '@/stores/category'
import type { Banner } from '@/types'

const router = useRouter()
const categoryStore = useCategoryStore()

// 计算属性
const banners = computed(() => categoryStore.activeBanners)
const loading = computed(() => categoryStore.loading)

// 处理轮播图点击
const handleBannerClick = (banner: Banner) => {
  if (banner.type === 'content' && banner.contentId) {
    router.push(`/content/${banner.contentId}`)
  } else if (banner.type === 'url' && banner.link) {
    window.open(banner.link, '_blank')
  }
}
</script>

<style scoped>
.banner-swiper {
  @apply mb-6;
}

:deep(.van-swipe__indicator) {
  @apply bg-white/50;
}

:deep(.van-swipe__indicator--active) {
  @apply bg-white;
}
</style>
