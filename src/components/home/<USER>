<template>
  <div class="recent-update">
    <!-- 标题 -->
    <div class="flex items-center justify-between mb-4">
      <h2 class="text-lg font-bold text-gray-900 flex items-center">
        <van-icon name="clock" class="text-blue-500 mr-2" />
        最近更新
      </h2>
      <router-link
        to="/category"
        class="text-sm text-primary-600 hover:text-primary-700 flex items-center"
      >
        更多
        <van-icon name="arrow" class="ml-1" />
      </router-link>
    </div>
    
    <!-- 更新列表 -->
    <div class="space-y-3">
      <div
        v-for="content in recentContents"
        :key="content.id"
        class="flex items-center space-x-3 p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer"
        @click="handleContentClick(content)"
      >
        <!-- 封面 -->
        <div class="flex-shrink-0">
          <van-image
            :src="content.cover"
            :alt="content.title"
            fit="cover"
            class="w-16 h-20 rounded"
            loading-icon="photo"
            error-icon="photo-fail"
          />
        </div>
        
        <!-- 内容信息 -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2 mb-1">
            <h3 class="font-medium text-gray-900 text-ellipsis">{{ content.title }}</h3>
            <van-tag
              :color="content.type === 'comic' ? '#ff6b6b' : '#4ecdc4'"
              size="mini"
              class="text-xs text-white flex-shrink-0"
            >
              {{ content.type === 'comic' ? '漫画' : '小说' }}
            </van-tag>
          </div>
          
          <p class="text-sm text-gray-500 mb-1 text-ellipsis">{{ content.author }}</p>
          
          <div class="flex items-center justify-between text-xs text-gray-400">
            <span class="text-ellipsis">最新：{{ content.lastChapterTitle }}</span>
            <span class="flex-shrink-0 ml-2">{{ formatTime(content.lastUpdateTime) }}</span>
          </div>
          
          <!-- 标签 -->
          <div v-if="content.tags && content.tags.length" class="flex flex-wrap gap-1 mt-2">
            <van-tag
              v-for="tag in content.tags.slice(0, 3)"
              :key="tag"
              size="mini"
              type="primary"
              plain
              class="text-xs"
            >
              {{ tag }}
            </van-tag>
          </div>
        </div>
        
        <!-- 状态和统计 -->
        <div class="flex-shrink-0 text-right">
          <van-tag
            :type="getStatusTagType(content.status)"
            size="mini"
            class="mb-2"
          >
            {{ getStatusText(content.status) }}
          </van-tag>
          
          <div class="text-xs text-gray-400 space-y-1">
            <div>{{ formatViewCount(content.viewCount) }}阅读</div>
            <div>{{ content.chapterCount }}章</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="space-y-3">
      <div
        v-for="i in 5"
        :key="i"
        class="flex items-center space-x-3 p-3 bg-white rounded-lg animate-pulse"
      >
        <div class="w-16 h-20 bg-gray-200 rounded"></div>
        <div class="flex-1 space-y-2">
          <div class="h-4 bg-gray-200 rounded"></div>
          <div class="h-3 bg-gray-200 rounded w-2/3"></div>
          <div class="h-3 bg-gray-200 rounded w-1/2"></div>
        </div>
        <div class="w-16 space-y-2">
          <div class="h-6 bg-gray-200 rounded"></div>
          <div class="h-3 bg-gray-200 rounded"></div>
        </div>
      </div>
    </div>
    
    <!-- 空状态 -->
    <div v-if="!recentContents.length && !loading" class="text-center py-12">
      <van-empty description="暂无更新内容" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useContentStore } from '@/stores/content'
import type { Content, ContentStatus } from '@/types'

const router = useRouter()
const contentStore = useContentStore()

// 状态
const recentContents = ref<Content[]>([])
const loading = ref(false)

// 获取最近更新内容
const getRecentContents = async () => {
  try {
    loading.value = true
    const response = await contentStore.getContentList({
      page: 1,
      pageSize: 10,
      sortBy: 'update',
      sortOrder: 'desc'
    })
    recentContents.value = response.list
  } catch (error) {
    console.error('获取最近更新失败:', error)
  } finally {
    loading.value = false
  }
}

// 处理内容点击
const handleContentClick = (content: Content) => {
  router.push(`/content/${content.id}`)
}

// 获取状态标签类型
const getStatusTagType = (status: ContentStatus) => {
  switch (status) {
    case 'ongoing':
      return 'success'
    case 'completed':
      return 'primary'
    case 'paused':
      return 'warning'
    default:
      return 'default'
  }
}

// 获取状态文本
const getStatusText = (status: ContentStatus) => {
  switch (status) {
    case 'ongoing':
      return '连载中'
    case 'completed':
      return '已完结'
    case 'paused':
      return '暂停'
    default:
      return '未知'
  }
}

// 格式化阅读数
const formatViewCount = (count: number) => {
  if (count >= 10000) {
    return `${(count / 10000).toFixed(1)}万`
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}k`
  }
  return count.toString()
}

// 格式化时间
const formatTime = (timeStr: string) => {
  const time = new Date(timeStr)
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else if (days < 30) {
    return `${days}天前`
  } else {
    return time.toLocaleDateString()
  }
}

onMounted(() => {
  getRecentContents()
})
</script>

<style scoped>
.recent-update {
  @apply mb-8;
}
</style>
