<template>
  <div class="app-layout">
    <!-- 顶部导航 -->
    <AppHeader />

    <!-- 主要内容区域 -->
    <main class="main-content">
      <router-view />
    </main>

    <!-- 页脚 -->
    <AppFooter />

    <!-- 移动端底部导航 -->
    <AppTabbar />

    <!-- 回到顶部按钮 -->
    <van-back-top v-show="showBackTop" :offset="100" class="back-top-btn" />

    <!-- 全局加载状态 -->
    <van-overlay :show="globalLoading" class="loading-overlay">
      <div class="loading-content">
        <van-loading size="24px" color="#0ea5e9">加载中...</van-loading>
      </div>
    </van-overlay>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useGlobalStore } from '@/stores/global'
import AppHeader from './AppHeader.vue'
import AppFooter from './AppFooter.vue'
import AppTabbar from './AppTabbar.vue'

const globalStore = useGlobalStore()
const showBackTop = ref(false)

// 计算属性
const globalLoading = computed(() => globalStore.loading)

// 监听滚动事件，控制回到顶部按钮显示
const handleScroll = () => {
  showBackTop.value = window.scrollY > 300
}

onMounted(() => {
  window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
.app-layout {
  @apply min-h-screen flex flex-col bg-gray-50;
}

.main-content {
  @apply flex-1 pb-16 md:pb-0;
}

.back-top-btn {
  --van-back-top-background: #0ea5e9;
  --van-back-top-color: #fff;
}

.loading-overlay {
  @apply z-50;
}

.loading-content {
  @apply flex items-center justify-center h-full;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .main-content {
    @apply pt-0;
  }
}
</style>
