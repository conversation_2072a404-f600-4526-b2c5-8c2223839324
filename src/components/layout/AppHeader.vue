<template>
  <header class="app-header">
    <!-- 桌面端导航 -->
    <div class="desktop-nav hidden md:block">
      <div class="container mx-auto px-4">
        <div class="flex items-center justify-between h-16">
          <!-- Logo -->
          <router-link to="/" class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-primary-500 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold text-lg">墨</span>
            </div>
            <span class="text-xl font-bold text-gray-800">墨文阁</span>
          </router-link>

          <!-- 导航菜单 -->
          <nav class="flex items-center space-x-8">
            <router-link 
              to="/" 
              class="nav-link"
              :class="{ 'active': $route.path === '/' }"
            >
              首页
            </router-link>
            <router-link 
              to="/category/comic" 
              class="nav-link"
              :class="{ 'active': $route.path.includes('/category/comic') }"
            >
              漫画
            </router-link>
            <router-link 
              to="/category/novel" 
              class="nav-link"
              :class="{ 'active': $route.path.includes('/category/novel') }"
            >
              小说
            </router-link>
            <router-link 
              to="/ranking" 
              class="nav-link"
              :class="{ 'active': $route.path === '/ranking' }"
            >
              排行榜
            </router-link>
          </nav>

          <!-- 搜索框 -->
          <div class="flex-1 max-w-md mx-8">
            <div class="relative">
              <input
                v-model="searchKeyword"
                type="text"
                placeholder="搜索漫画、小说或作者..."
                class="w-full px-4 py-2 pl-10 pr-4 text-sm border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                @keyup.enter="handleSearch"
              >
              <van-icon 
                name="search" 
                class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              />
            </div>
          </div>

          <!-- 用户菜单 -->
          <div class="flex items-center space-x-4">
            <template v-if="userStore.isLoggedIn">
              <!-- 已登录状态 -->
              <div class="relative" ref="userMenuRef">
                <button
                  @click="showUserMenu = !showUserMenu"
                  class="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <van-image
                    :src="userStore.userInfo?.avatar || '/default-avatar.png'"
                    class="w-8 h-8 rounded-full"
                    fit="cover"
                  />
                  <span class="text-sm text-gray-700">{{ userStore.userInfo?.nickname }}</span>
                  <van-icon name="arrow-down" class="text-gray-400" />
                </button>
                
                <!-- 用户下拉菜单 -->
                <div
                  v-show="showUserMenu"
                  class="absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50"
                >
                  <router-link
                    to="/profile"
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    @click="showUserMenu = false"
                  >
                    个人中心
                  </router-link>
                  <router-link
                    to="/favorites"
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    @click="showUserMenu = false"
                  >
                    我的收藏
                  </router-link>
                  <router-link
                    to="/history"
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    @click="showUserMenu = false"
                  >
                    阅读历史
                  </router-link>
                  <div class="border-t border-gray-200 my-2"></div>
                  <button
                    @click="handleLogout"
                    class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                  >
                    退出登录
                  </button>
                </div>
              </div>
            </template>
            <template v-else>
              <!-- 未登录状态 -->
              <router-link
                to="/login"
                class="px-4 py-2 text-sm text-primary-600 hover:text-primary-700 transition-colors"
              >
                登录
              </router-link>
              <router-link
                to="/register"
                class="px-4 py-2 text-sm bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors"
              >
                注册
              </router-link>
            </template>
          </div>
        </div>
      </div>
    </div>

    <!-- 移动端导航 -->
    <div class="mobile-nav md:hidden">
      <van-nav-bar
        :title="pageTitle"
        left-arrow
        @click-left="handleBack"
      >
        <template #right>
          <van-icon name="search" @click="showMobileSearch = true" />
        </template>
      </van-nav-bar>
    </div>

    <!-- 移动端搜索弹窗 -->
    <van-popup
      v-model:show="showMobileSearch"
      position="top"
      :style="{ height: '100%' }"
    >
      <div class="p-4">
        <div class="flex items-center space-x-2 mb-4">
          <van-search
            v-model="searchKeyword"
            placeholder="搜索漫画、小说或作者..."
            @search="handleMobileSearch"
            @cancel="showMobileSearch = false"
            show-action
          />
        </div>
        <!-- 搜索建议和历史记录可以在这里添加 -->
      </div>
    </van-popup>
  </header>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 响应式数据
const searchKeyword = ref('')
const showUserMenu = ref(false)
const showMobileSearch = ref(false)
const userMenuRef = ref<HTMLElement>()

// 计算属性
const pageTitle = computed(() => {
  const titleMap: Record<string, string> = {
    '/': '墨文阁',
    '/category/comic': '漫画分类',
    '/category/novel': '小说分类',
    '/ranking': '排行榜',
    '/search': '搜索',
    '/profile': '个人中心',
    '/favorites': '我的收藏',
    '/history': '阅读历史',
  }
  return titleMap[route.path] || '墨文阁'
})

// 方法
const handleSearch = () => {
  if (searchKeyword.value.trim()) {
    router.push({
      path: '/search',
      query: { keyword: searchKeyword.value.trim() }
    })
  }
}

const handleMobileSearch = () => {
  handleSearch()
  showMobileSearch.value = false
}

const handleBack = () => {
  if (window.history.length > 1) {
    router.back()
  } else {
    router.push('/')
  }
}

const handleLogout = () => {
  userStore.logout()
  showUserMenu.value = false
  router.push('/')
}

// 点击外部关闭用户菜单
const handleClickOutside = (event: Event) => {
  if (userMenuRef.value && !userMenuRef.value.contains(event.target as Node)) {
    showUserMenu.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.app-header {
  @apply bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40;
}

.nav-link {
  @apply text-gray-600 hover:text-primary-600 font-medium transition-colors duration-200 relative;
}

.nav-link.active {
  @apply text-primary-600;
}

.nav-link.active::after {
  content: '';
  @apply absolute bottom-0 left-0 right-0 h-0.5 bg-primary-500;
  transform: translateY(100%);
}
</style>
