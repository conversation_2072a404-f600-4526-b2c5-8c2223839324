<template>
  <van-tabbar 
    v-model="activeTab" 
    class="app-tabbar md:hidden"
    fixed
    placeholder
    safe-area-inset-bottom
  >
    <van-tabbar-item 
      to="/" 
      icon="home-o"
      name="home"
    >
      首页
    </van-tabbar-item>
    
    <van-tabbar-item 
      to="/category" 
      icon="apps-o"
      name="category"
    >
      分类
    </van-tabbar-item>
    
    <van-tabbar-item 
      to="/ranking" 
      icon="bar-chart-o"
      name="ranking"
    >
      排行
    </van-tabbar-item>
    
    <van-tabbar-item 
      to="/bookshelf" 
      icon="bookmark-o"
      name="bookshelf"
    >
      书架
    </van-tabbar-item>
    
    <van-tabbar-item 
      to="/profile" 
      icon="user-o"
      name="profile"
    >
      我的
    </van-tabbar-item>
  </van-tabbar>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const activeTab = ref('home')

// 根据当前路由设置活跃标签
const setActiveTab = () => {
  const path = route.path
  if (path === '/') {
    activeTab.value = 'home'
  } else if (path.startsWith('/category')) {
    activeTab.value = 'category'
  } else if (path === '/ranking') {
    activeTab.value = 'ranking'
  } else if (path === '/bookshelf' || path === '/favorites' || path === '/history') {
    activeTab.value = 'bookshelf'
  } else if (path.startsWith('/profile') || path === '/login' || path === '/register') {
    activeTab.value = 'profile'
  }
}

// 监听路由变化
watch(() => route.path, setActiveTab, { immediate: true })
</script>

<style scoped>
.app-tabbar {
  --van-tabbar-background: #fff;
  --van-tabbar-item-text-color: #969799;
  --van-tabbar-item-active-color: #0ea5e9;
  --van-tabbar-item-active-background: transparent;
}
</style>
