<template>
  <footer class="app-footer hidden md:block">
    <div class="container mx-auto px-4 py-12">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <!-- 网站信息 -->
        <div class="col-span-1 md:col-span-2">
          <div class="flex items-center space-x-2 mb-4">
            <div class="w-8 h-8 bg-primary-500 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold text-lg">墨</span>
            </div>
            <span class="text-xl font-bold text-gray-800">墨文阁</span>
          </div>
          <p class="text-gray-600 mb-4 max-w-md">
            专业的漫画小说阅读平台，为您提供海量优质内容，支持多种阅读模式，打造极致阅读体验。
          </p>
          <div class="flex space-x-4">
            <a href="#" class="text-gray-400 hover:text-primary-500 transition-colors">
              <van-icon name="wechat" size="20" />
            </a>
            <a href="#" class="text-gray-400 hover:text-primary-500 transition-colors">
              <van-icon name="weibo" size="20" />
            </a>
            <a href="#" class="text-gray-400 hover:text-primary-500 transition-colors">
              <van-icon name="qq" size="20" />
            </a>
          </div>
        </div>

        <!-- 快速链接 -->
        <div>
          <h3 class="text-lg font-semibold text-gray-800 mb-4">快速链接</h3>
          <ul class="space-y-2">
            <li>
              <router-link to="/category/comic" class="footer-link">
                漫画分类
              </router-link>
            </li>
            <li>
              <router-link to="/category/novel" class="footer-link">
                小说分类
              </router-link>
            </li>
            <li>
              <router-link to="/ranking" class="footer-link">
                排行榜
              </router-link>
            </li>
            <li>
              <router-link to="/latest" class="footer-link">
                最新更新
              </router-link>
            </li>
          </ul>
        </div>

        <!-- 帮助支持 -->
        <div>
          <h3 class="text-lg font-semibold text-gray-800 mb-4">帮助支持</h3>
          <ul class="space-y-2">
            <li>
              <router-link to="/help" class="footer-link">
                使用帮助
              </router-link>
            </li>
            <li>
              <router-link to="/feedback" class="footer-link">
                意见反馈
              </router-link>
            </li>
            <li>
              <router-link to="/contact" class="footer-link">
                联系我们
              </router-link>
            </li>
            <li>
              <router-link to="/about" class="footer-link">
                关于我们
              </router-link>
            </li>
          </ul>
        </div>
      </div>

      <!-- 分割线 -->
      <div class="border-t border-gray-200 mt-8 pt-8">
        <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <div class="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-6">
            <p class="text-sm text-gray-500">
              © 2024 墨文阁. All rights reserved.
            </p>
            <div class="flex space-x-4 text-sm">
              <router-link to="/privacy" class="text-gray-500 hover:text-primary-500 transition-colors">
                隐私政策
              </router-link>
              <router-link to="/terms" class="text-gray-500 hover:text-primary-500 transition-colors">
                服务条款
              </router-link>
              <router-link to="/copyright" class="text-gray-500 hover:text-primary-500 transition-colors">
                版权声明
              </router-link>
            </div>
          </div>
          
          <div class="flex items-center space-x-2 text-sm text-gray-500">
            <span>备案号：</span>
            <a href="#" class="hover:text-primary-500 transition-colors">
              京ICP备12345678号
            </a>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
// 页脚组件不需要额外的逻辑
</script>

<style scoped>
.app-footer {
  @apply bg-gray-50 border-t border-gray-200 mt-auto;
}

.footer-link {
  @apply text-gray-600 hover:text-primary-500 transition-colors duration-200 text-sm;
}
</style>
