<template>
  <div class="content-card" @click="handleClick">
    <div class="relative overflow-hidden rounded-lg bg-gray-100">
      <!-- 封面图片 -->
      <van-image
        :src="content.cover"
        :alt="content.title"
        fit="cover"
        class="w-full aspect-[3/4]"
        loading-icon="photo"
        error-icon="photo-fail"
      />
      
      <!-- 状态标签 -->
      <div class="absolute top-2 left-2">
        <van-tag
          :type="getStatusTagType(content.status)"
          size="mini"
          class="text-xs"
        >
          {{ getStatusText(content.status) }}
        </van-tag>
      </div>
      
      <!-- 评分 -->
      <div v-if="content.rating > 0" class="absolute top-2 right-2 bg-black/50 text-white px-2 py-1 rounded text-xs">
        <van-icon name="star" class="mr-1" />
        {{ content.rating.toFixed(1) }}
      </div>
      
      <!-- 类型标识 -->
      <div class="absolute bottom-2 left-2">
        <van-tag
          :color="content.type === 'comic' ? '#ff6b6b' : '#4ecdc4'"
          size="mini"
          class="text-xs text-white"
        >
          {{ content.type === 'comic' ? '漫画' : '小说' }}
        </van-tag>
      </div>
    </div>
    
    <!-- 内容信息 -->
    <div class="mt-3 space-y-1">
      <h3 class="font-medium text-gray-900 text-sm leading-tight text-ellipsis-2">
        {{ content.title }}
      </h3>
      
      <p class="text-xs text-gray-500 text-ellipsis">
        {{ content.author }}
      </p>
      
      <div class="flex items-center justify-between text-xs text-gray-400">
        <span>{{ formatViewCount(content.viewCount) }}阅读</span>
        <span>{{ content.chapterCount }}章</span>
      </div>
      
      <!-- 标签 -->
      <div v-if="content.tags && content.tags.length" class="flex flex-wrap gap-1 mt-2">
        <van-tag
          v-for="tag in content.tags.slice(0, 2)"
          :key="tag"
          size="mini"
          type="primary"
          plain
          class="text-xs"
        >
          {{ tag }}
        </van-tag>
      </div>
      
      <!-- 最新章节 -->
      <div v-if="content.lastChapterTitle" class="text-xs text-gray-500 text-ellipsis">
        最新：{{ content.lastChapterTitle }}
      </div>
      
      <!-- 更新时间 -->
      <div class="text-xs text-gray-400">
        {{ formatTime(content.lastUpdateTime) }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import type { Content, ContentStatus } from '@/types'

interface Props {
  content: Content
}

const props = defineProps<Props>()
const router = useRouter()

// 处理点击事件
const handleClick = () => {
  router.push(`/content/${props.content.id}`)
}

// 获取状态标签类型
const getStatusTagType = (status: ContentStatus) => {
  switch (status) {
    case 'ongoing':
      return 'success'
    case 'completed':
      return 'primary'
    case 'paused':
      return 'warning'
    default:
      return 'default'
  }
}

// 获取状态文本
const getStatusText = (status: ContentStatus) => {
  switch (status) {
    case 'ongoing':
      return '连载中'
    case 'completed':
      return '已完结'
    case 'paused':
      return '暂停'
    default:
      return '未知'
  }
}

// 格式化阅读数
const formatViewCount = (count: number) => {
  if (count >= 10000) {
    return `${(count / 10000).toFixed(1)}万`
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}k`
  }
  return count.toString()
}

// 格式化时间
const formatTime = (timeStr: string) => {
  const time = new Date(timeStr)
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else if (days < 30) {
    return `${days}天前`
  } else {
    return time.toLocaleDateString()
  }
}
</script>

<style scoped>
.content-card {
  @apply cursor-pointer transition-transform duration-200 hover:scale-105;
}

.content-card:active {
  @apply scale-95;
}
</style>
